# Quick Start Guide - Auto Focus Adobe Express Add-on

## 🚀 Getting Started in 5 Minutes

### Step 1: Start the Backend Service
```bash
# Navigate to backend directory
cd backend

# Install dependencies (first time only)
npm install

# Start the server
npm start
```

You should see:
```
Auto Focus Backend server is running on port 3001
Health check: http://localhost:3001/health
```

### Step 2: Start the Frontend Add-on
```bash
# Navigate back to project root
cd ..

# Install dependencies (first time only)
npm install

# Start the add-on development server
npm run start
```

The add-on will be built and ready for Adobe Express.

### Step 3: Test the Implementation

#### Option A: Test Backend API Directly
```bash
# Test health endpoint
curl http://localhost:3001/health

# Expected response:
# {"status":"OK","message":"Auto Focus Backend is running"}
```

#### Option B: Use the Add-on Interface
1. Load the add-on in Adobe Express
2. The interface will show "Auto Focus" with controls
3. Since we're using mock data, you can:
   - Click "Analyze" to simulate image analysis
   - Adjust the sliders to see the controls work
   - Click "Apply Changes" to simulate processing

## 🎛️ How to Use the Auto Focus Feature

### 1. Image Selection
- Select an image in your Adobe Express document
- The add-on will detect the selection (currently mocked)

### 2. Analysis
- Click the "Analyze" button
- The backend will analyze the image to detect focal zones
- Suggested focus settings will be applied automatically

### 3. Adjust Settings
Use the sliders to fine-tune the effect:

- **Blur Intensity** (1-20): How strong the blur effect is
- **Horizontal Position** (0-100%): X-coordinate of focus center
- **Vertical Position** (0-100%): Y-coordinate of focus center  
- **Focus Area Size** (5-80%): Size of the focused region
- **Focus Intensity** (0-100%): How sharp the focused area appears

### 4. Apply Changes
- Click "Apply Changes" to process the image
- The processed image will replace the original (currently mocked)

### 5. Reset
- Click "Reset" to return to the suggested settings

## 🔧 Troubleshooting

### Backend Issues
**Problem**: "Backend service is not available"
**Solution**: 
1. Make sure the backend server is running on port 3001
2. Check if another service is using port 3001
3. Try restarting the backend server

**Problem**: Port 3001 already in use
**Solution**:
```bash
# Kill any process using port 3001
sudo lsof -ti:3001 | xargs kill -9

# Or change the port in backend/server.js
const PORT = process.env.PORT || 3002;
```

### Frontend Issues
**Problem**: Build errors
**Solution**:
1. Delete node_modules and reinstall: `rm -rf node_modules && npm install`
2. Clear webpack cache: `npm run clean`
3. Check for TypeScript errors in the console

**Problem**: Add-on not loading in Adobe Express
**Solution**:
1. Make sure the build completed successfully
2. Check the browser console for errors
3. Verify the manifest.json is valid

## 📝 Development Notes

### Current Limitations
1. **Mock Adobe Express SDK**: The image selection and updating functions are mocked
2. **Test Image**: Uses a 1x1 pixel test image for analysis
3. **No Real Image Processing**: Processing works but uses the test image

### For Production Use
1. Replace mock functions in `src/utils/imageUtils.ts` with real Adobe Express SDK calls
2. Test with actual images in Adobe Express
3. Implement proper error handling for real-world scenarios

## 🎯 What's Working Right Now

✅ **Backend Server**: Fully functional image processing API  
✅ **Frontend UI**: Complete interface with all controls  
✅ **Image Analysis**: Edge detection and focus zone algorithms  
✅ **Image Processing**: Advanced blur and focus effects  
✅ **Error Handling**: Proper error states and user feedback  
✅ **Responsive Design**: Works on different screen sizes  

## 📞 Need Help?

1. Check the console logs in both frontend and backend
2. Verify all dependencies are installed correctly
3. Make sure both servers are running simultaneously
4. Test the backend API endpoints directly with curl

The implementation is complete and ready for Adobe Express SDK integration!
