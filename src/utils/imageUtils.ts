import { AddOnSDKAPI } from "https://new.express.adobe.com/static/add-on-sdk/sdk.js";

/**
 * Gets the currently selected image from Adobe Express
 * @param addOnUISdk Adobe Express SDK instance
 * @returns Promise with image data as base64 string
 */
export async function getSelectedImage(addOnUISdk: AddOnSDKAPI): Promise<string | null> {
    try {
        // For now, return a mock base64 image for testing
        // In a real implementation, this would use the actual Adobe Express SDK API
        console.log('Getting selected image from Adobe Express SDK...');

        // Mock implementation - replace with actual SDK calls when available
        // This is a 1x1 pixel transparent PNG for testing
        const mockImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

        // TODO: Replace with actual Adobe Express SDK implementation
        // const document = addOnUISdk.app.document;
        // const selection = await document.getSelection();
        // ... actual implementation

        return mockImageData;

    } catch (error) {
        console.error('Error getting selected image:', error);
        throw new Error('Failed to get selected image. This is a mock implementation.');
    }
}

/**
 * Updates the selected image in Adobe Express with processed image data
 * @param addOnUISdk Adobe Express SDK instance
 * @param processedImageData Base64 encoded processed image
 * @returns Promise<void>
 */
export async function updateSelectedImage(
    addOnUISdk: AddOnSDKAPI,
    processedImageData: string
): Promise<void> {
    try {
        // Mock implementation for testing
        console.log('Updating selected image in Adobe Express SDK...');
        console.log('Processed image data length:', processedImageData.length);

        // TODO: Replace with actual Adobe Express SDK implementation
        // const document = addOnUISdk.app.document;
        // const selection = await document.getSelection();
        // ... actual implementation

        // For now, just log success
        console.log('Image updated successfully (mock implementation)');

    } catch (error) {
        console.error('Error updating selected image:', error);
        throw new Error('Failed to update selected image. This is a mock implementation.');
    }
}

/**
 * Converts ArrayBuffer to base64 string
 * @param buffer ArrayBuffer to convert
 * @returns Base64 string
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

/**
 * Converts base64 data URL to Blob
 * @param dataUrl Base64 data URL
 * @returns Blob object
 */
export function base64ToBlob(dataUrl: string): Blob {
    const arr = dataUrl.split(',');
    const mimeMatch = arr[0].match(/:(.*?);/);
    const mime = mimeMatch ? mimeMatch[1] : 'image/jpeg';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);

    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }

    return new Blob([u8arr], { type: mime });
}

/**
 * Validates if the current selection contains an image
 * @param addOnUISdk Adobe Express SDK instance
 * @returns Promise<boolean>
 */
export async function hasSelectedImage(addOnUISdk: AddOnSDKAPI): Promise<boolean> {
    try {
        // Mock implementation for testing
        console.log('Checking for selected image in Adobe Express SDK...');

        // TODO: Replace with actual Adobe Express SDK implementation
        // const document = addOnUISdk.app.document;
        // const selection = await document.getSelection();
        // return selection.some((item: any) => item.type === 'image');

        // For testing, always return true
        return true;

    } catch (error) {
        console.error('Error checking for selected image:', error);
        return false;
    }
}

/**
 * Gets image dimensions from base64 data
 * @param base64Data Base64 encoded image data
 * @returns Promise with width and height
 */
export function getImageDimensions(base64Data: string): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
        const img = new Image();

        img.onload = () => {
            resolve({
                width: img.naturalWidth,
                height: img.naturalHeight
            });
        };

        img.onerror = () => {
            reject(new Error('Failed to load image for dimension calculation'));
        };

        img.src = base64Data;
    });
}
